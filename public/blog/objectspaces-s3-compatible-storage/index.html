<!DOCTYPE html>
<html lang="en-us">
  <head><script src="/livereload.js?mindelay=10&amp;v=2&amp;port=1313&amp;path=livereload" data-no-instant defer></script>
    

<meta property="og:url" content="http://localhost:1313/blog/objectspaces-s3-compatible-storage/">
  <meta property="og:site_name" content="whitesky cloud platform">
  <meta property="og:title" content="ObjectSpaces: Scalable S3-Compatible Storage">
  <meta property="og:description" content="Learn how ObjectSpaces provide scalable, S3-compatible object storage with advanced features like versioning and object locking for enterprise workloads.">
  <meta property="og:locale" content="en_us">
  <meta property="og:type" content="article">
    <meta property="article:section" content="blog">
    <meta property="article:published_time" content="2024-01-25T00:00:00+00:00">
    <meta property="article:modified_time" content="2024-01-25T00:00:00+00:00">
    <meta property="article:tag" content="Objectspaces">
    <meta property="article:tag" content="Storage">
    <meta property="article:tag" content="S3">
    <meta property="article:tag" content="Backup">


<meta name="description" content="Learn how ObjectSpaces provide scalable, S3-compatible object storage with advanced features like versioning and object locking for enterprise workloads." />
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
<meta http-equiv="x-ua-compatible" content="ie=edge">
    <title>whitesky cloud platform</title>
    
  
<link
  rel="stylesheet"
  href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css"
  integrity="sha512-Avb2QiuDEEvB4bZJYdft2mNjVShBftLdPG8FJ0V7irTLQ8Uo0qcPxh4Plq7G5tGm0rU+1SPhVotteLpBERwTkw=="
  crossorigin="anonymous"
  referrerpolicy="no-referrer"
/>
<link rel="icon" type="image/png" href="/images/favicon.png" />
<link href="https://fonts.googleapis.com/css?family=Open&#43;Sans:400,600" rel="stylesheet">
<link rel="stylesheet" type="text/css" href="/css/style.css">
<link rel="stylesheet" type="text/css" href="/css/icons.css">

  </head>
  <body>
    
    
    <div id="preloader">
      <div id="status"></div>
    </div>
    

    


<nav class="navbar is-fresh is-transparent no-shadow" role="navigation" aria-label="main navigation">
  <div class="container">
    <div class="navbar-brand">
      <a class="navbar-item" href="/">
        <img src="/images/logo-blue-blacktext-small.png" alt="" width="160" height="28">
      </a>

      <a role="button" class="navbar-burger" aria-label="menu" aria-expanded="false" data-target="navbar-menu">
        <span aria-hidden="true"></span>
        <span aria-hidden="true"></span>
        <span aria-hidden="true"></span>
      </a>
    </div>

      <div id="navbar-menu" class="navbar-menu is-static">

        <div class="navbar-end">
          <a href="/" class="navbar-item is-secondary">
            Features
          </a>
          <a href="/" class="navbar-item is-secondary">
            Pricing
          </a>
          <a href="/blog#blog" class="navbar-item is-secondary">
            Blog
          </a>
          <div class="navbar-item has-dropdown is-hoverable">
            <a class="navbar-link">
              Resources
            </a>

            <div class="navbar-dropdown">
              <a href="/" class="navbar-item">
                Documentation
              </a>
              <a href="/" class="navbar-item">
                Support
              </a>
              <a href="/partner-portal" class="navbar-item">
                Partner Portal
              </a>
            </div>
          </div>
          <a href="/" class="navbar-item is-secondary">
            Log in
          </a>
          <a href="/" class="navbar-item">
            <span class="button signup-button rounded secondary-btn raised">
              Try now
            </span>
          </a>
        </div>
      </div>
  </div>
</nav>


<nav id="navbar-clone" class="navbar is-fresh is-transparent" role="navigation" aria-label="main navigation">
  <div class="container">
    <div class="navbar-brand">
      <a class="navbar-item" href="/">
        <img src="/images/logo-blue-blacktext-small.png" alt="" width="160" height="160">
      </a>

      <a role="button" class="navbar-burger" aria-label="menu" aria-expanded="false" data-target="cloned-navbar-menu">
        <span aria-hidden="true"></span>
        <span aria-hidden="true"></span>
        <span aria-hidden="true"></span>
      </a>
    </div>

    <div id="cloned-navbar-menu" class="navbar-menu is-fixed">

      <div class="navbar-end">
        <a href="/" class="navbar-item is-secondary">
          Features
        </a>
        <a href="/" class="navbar-item is-secondary">
          Pricing
        </a>
        <a href="/blog#blog" class="navbar-item is-secondary">
          Blog
        </a>
        <div class="navbar-item has-dropdown is-hoverable">
          <a class="navbar-link">
            Resources
          </a>

          <div class="navbar-dropdown">
            <a href="/" class="navbar-item">
              Documentation
            </a>
            <a href="/" class="navbar-item">
              Support
            </a>
            <a href="/partner-portal" class="navbar-item">
              Partner Portal
            </a>
          </div>
        </div>
        <a href="/" class="navbar-item is-secondary">
          Log in
        </a>
        <a href="/" class="navbar-item">
          <span class="button signup-button rounded secondary-btn raised">
            Try now
          </span>
        </a>
      </div>
    </div>
</div>
</nav>


<section class="section is-medium">
  <div class="container">
    
    <div class="columns">
      <div class="column is-8 is-offset-2">
        <div class="has-text-centered">
          <h1 class="title is-2 has-text-weight-bold">ObjectSpaces: Scalable S3-Compatible Storage</h1>
          
          <h2 class="subtitle is-4 has-text-grey">Enterprise-grade object storage with versioning and object locking</h2>
          
          
          <div class="article-meta">
            <p class="has-text-grey">
              <time datetime="2024-01-25">
                January 25, 2024
              </time>
              
              <span class="has-text-grey-light"> • </span>
              <span>Storage Team</span>
              
              <span class="has-text-grey-light"> • </span>
              <span>4 min read</span>
            </p>
          </div>
          
          
          <div class="tags is-centered" style="margin-top: 1rem;">
            
            <span class="tag is-primary is-light">objectspaces</span>
            
            <span class="tag is-primary is-light">storage</span>
            
            <span class="tag is-primary is-light">s3</span>
            
            <span class="tag is-primary is-light">backup</span>
            
          </div>
          
        </div>
        
        <div class="divider  is-centered" style="margin-top: 1rem;"></div>
      </div>
    </div>

    
    
    <div class="columns">
      <div class="column is-10 is-offset-1">
        <figure class="image">
          <img src="/images/cloud-worker-small.jpg" alt="ObjectSpaces: Scalable S3-Compatible Storage" style="border-radius: 6px;">
        </figure>
      </div>
    </div>
    

    
    <div class="columns">
      <div class="column is-8 is-offset-2">
        <div class="content is-medium">
          <h1 id="objectspaces-the-foundation-of-modern-data-storage">ObjectSpaces: The Foundation of Modern Data Storage</h1>
<p>In today&rsquo;s data-driven world, organizations need storage solutions that can scale seamlessly while providing the reliability and features required for enterprise workloads. ObjectSpaces deliver exactly that—scalable, S3-compatible object storage with enterprise-grade features built in.</p>
<h2 id="what-are-objectspaces">What Are ObjectSpaces?</h2>
<p>ObjectSpaces are WhiteSky Cloud&rsquo;s answer to modern object storage needs, providing:</p>
<ul>
<li><strong>S3-Compatible API</strong> for seamless integration</li>
<li><strong>Versioning</strong> for data protection and compliance</li>
<li><strong>Object Locking</strong> for regulatory requirements</li>
<li><strong>Scalability</strong> up to 360PB per deployment</li>
<li><strong>Multi-Site Replication</strong> for disaster recovery</li>
<li><strong>Lifecycle Management</strong> for cost optimization</li>
</ul>
<h2 id="key-features">Key Features</h2>
<h3 id="s3-compatibility">S3 Compatibility</h3>
<p>ObjectSpaces implement the S3 API, ensuring compatibility with:</p>
<ul>
<li><strong>AWS SDK</strong> and tools</li>
<li><strong>Third-party applications</strong> that support S3</li>
<li><strong>Backup solutions</strong> like Veeam, Commvault, and others</li>
<li><strong>Content delivery networks</strong> and static website hosting</li>
</ul>
<h3 id="versioning-and-object-locking">Versioning and Object Locking</h3>
<p>Protect your data with advanced features:</p>
<div class="highlight"><pre tabindex="0" style="color:#f8f8f2;background-color:#272822;-moz-tab-size:4;-o-tab-size:4;tab-size:4;"><code class="language-bash" data-lang="bash"><span style="display:flex;"><span><span style="color:#75715e"># Enable versioning on a bucket</span>
</span></span><span style="display:flex;"><span>aws s3api put-bucket-versioning <span style="color:#ae81ff">\
</span></span></span><span style="display:flex;"><span><span style="color:#ae81ff"></span>  --bucket my-bucket <span style="color:#ae81ff">\
</span></span></span><span style="display:flex;"><span><span style="color:#ae81ff"></span>  --versioning-configuration Status<span style="color:#f92672">=</span>Enabled
</span></span><span style="display:flex;"><span>
</span></span><span style="display:flex;"><span><span style="color:#75715e"># Set object lock configuration</span>
</span></span><span style="display:flex;"><span>aws s3api put-object-lock-configuration <span style="color:#ae81ff">\
</span></span></span><span style="display:flex;"><span><span style="color:#ae81ff"></span>  --bucket my-bucket <span style="color:#ae81ff">\
</span></span></span><span style="display:flex;"><span><span style="color:#ae81ff"></span>  --object-lock-configuration <span style="color:#ae81ff">\
</span></span></span><span style="display:flex;"><span><span style="color:#ae81ff"></span>  <span style="color:#e6db74">&#39;ObjectLockEnabled=Enabled,Rule={DefaultRetention={Mode=GOVERNANCE,Years=1}}&#39;</span>
</span></span></code></pre></div><h3 id="massive-scalability">Massive Scalability</h3>
<p>ObjectSpaces can scale to meet any storage requirement:</p>
<ul>
<li><strong>Start small</strong> with just a few terabytes</li>
<li><strong>Scale seamlessly</strong> to hundreds of petabytes</li>
<li><strong>Add capacity</strong> without downtime</li>
<li><strong>Distribute across</strong> multiple sites for redundancy</li>
</ul>
<h2 id="use-cases">Use Cases</h2>
<h3 id="backup-and-archive">Backup and Archive</h3>
<p>ObjectSpaces excel as a backup target:</p>
<ul>
<li><strong>Immutable backups</strong> with object locking</li>
<li><strong>Long-term retention</strong> with lifecycle policies</li>
<li><strong>Cost-effective</strong> storage for infrequently accessed data</li>
<li><strong>Integration</strong> with popular backup solutions</li>
</ul>
<h3 id="static-website-hosting">Static Website Hosting</h3>
<p>Host static websites and applications:</p>
<ul>
<li><strong>Fast content delivery</strong> with CDN integration</li>
<li><strong>SSL/TLS termination</strong> for secure connections</li>
<li><strong>Custom domains</strong> and routing rules</li>
<li><strong>Automatic scaling</strong> for traffic spikes</li>
</ul>
<h3 id="data-lake-and-analytics">Data Lake and Analytics</h3>
<p>Build modern data architectures:</p>
<ul>
<li><strong>Store structured and unstructured data</strong></li>
<li><strong>Integration</strong> with analytics platforms</li>
<li><strong>Data versioning</strong> for reproducible analysis</li>
<li><strong>Lifecycle management</strong> for cost optimization</li>
</ul>
<h3 id="application-storage">Application Storage</h3>
<p>Support cloud-native applications:</p>
<ul>
<li><strong>File uploads</strong> and user-generated content</li>
<li><strong>Media storage</strong> for streaming applications</li>
<li><strong>Document management</strong> systems</li>
<li><strong>API-driven</strong> storage operations</li>
</ul>
<h2 id="advanced-features">Advanced Features</h2>
<h3 id="lifecycle-management">Lifecycle Management</h3>
<p>Automatically manage data lifecycle:</p>
<div class="highlight"><pre tabindex="0" style="color:#f8f8f2;background-color:#272822;-moz-tab-size:4;-o-tab-size:4;tab-size:4;"><code class="language-json" data-lang="json"><span style="display:flex;"><span>{
</span></span><span style="display:flex;"><span>  <span style="color:#f92672">&#34;Rules&#34;</span>: [
</span></span><span style="display:flex;"><span>    {
</span></span><span style="display:flex;"><span>      <span style="color:#f92672">&#34;ID&#34;</span>: <span style="color:#e6db74">&#34;ArchiveOldData&#34;</span>,
</span></span><span style="display:flex;"><span>      <span style="color:#f92672">&#34;Status&#34;</span>: <span style="color:#e6db74">&#34;Enabled&#34;</span>,
</span></span><span style="display:flex;"><span>      <span style="color:#f92672">&#34;Transitions&#34;</span>: [
</span></span><span style="display:flex;"><span>        {
</span></span><span style="display:flex;"><span>          <span style="color:#f92672">&#34;Days&#34;</span>: <span style="color:#ae81ff">30</span>,
</span></span><span style="display:flex;"><span>          <span style="color:#f92672">&#34;StorageClass&#34;</span>: <span style="color:#e6db74">&#34;STANDARD_IA&#34;</span>
</span></span><span style="display:flex;"><span>        },
</span></span><span style="display:flex;"><span>        {
</span></span><span style="display:flex;"><span>          <span style="color:#f92672">&#34;Days&#34;</span>: <span style="color:#ae81ff">90</span>,
</span></span><span style="display:flex;"><span>          <span style="color:#f92672">&#34;StorageClass&#34;</span>: <span style="color:#e6db74">&#34;GLACIER&#34;</span>
</span></span><span style="display:flex;"><span>        }
</span></span><span style="display:flex;"><span>      ]
</span></span><span style="display:flex;"><span>    }
</span></span><span style="display:flex;"><span>  ]
</span></span><span style="display:flex;"><span>}
</span></span></code></pre></div><h3 id="multi-site-replication">Multi-Site Replication</h3>
<p>Ensure data availability across locations:</p>
<ul>
<li><strong>Cross-region replication</strong> for disaster recovery</li>
<li><strong>Bidirectional sync</strong> for active-active setups</li>
<li><strong>Selective replication</strong> based on prefixes or tags</li>
<li><strong>Conflict resolution</strong> for concurrent updates</li>
</ul>
<h3 id="security-and-access-control">Security and Access Control</h3>
<p>Comprehensive security features:</p>
<ul>
<li><strong>IAM integration</strong> for fine-grained access control</li>
<li><strong>Bucket policies</strong> for resource-based permissions</li>
<li><strong>Encryption at rest</strong> and in transit</li>
<li><strong>Audit logging</strong> for compliance requirements</li>
</ul>
<h2 id="performance-optimization">Performance Optimization</h2>
<h3 id="storage-classes">Storage Classes</h3>
<p>Choose the right storage class for your workload:</p>
<table>
  <thead>
      <tr>
          <th>Storage Class</th>
          <th>Use Case</th>
          <th>Availability</th>
      </tr>
  </thead>
  <tbody>
      <tr>
          <td>Standard</td>
          <td>Frequently accessed data</td>
          <td>99.99%</td>
      </tr>
      <tr>
          <td>Standard-IA</td>
          <td>Infrequently accessed data</td>
          <td>99.9%</td>
      </tr>
      <tr>
          <td>Glacier</td>
          <td>Long-term archive</td>
          <td>99.99%</td>
      </tr>
      <tr>
          <td>Deep Archive</td>
          <td>Rarely accessed archive</td>
          <td>99.99%</td>
      </tr>
  </tbody>
</table>
<h3 id="best-practices">Best Practices</h3>
<p>Optimize performance and costs:</p>
<ol>
<li><strong>Use appropriate storage classes</strong> for different data types</li>
<li><strong>Implement lifecycle policies</strong> to automatically transition data</li>
<li><strong>Enable compression</strong> for text-based content</li>
<li><strong>Use multipart uploads</strong> for large files</li>
<li><strong>Implement proper caching</strong> strategies</li>
</ol>
<h2 id="getting-started">Getting Started</h2>
<h3 id="creating-your-first-objectspace">Creating Your First ObjectSpace</h3>
<ol>
<li><strong>Access the Portal</strong>: Log into your WhiteSky Cloud portal</li>
<li><strong>Navigate to ObjectSpaces</strong>: Select the ObjectSpaces service</li>
<li><strong>Create a Bucket</strong>: Define your bucket name and configuration</li>
<li><strong>Configure Access</strong>: Set up IAM policies and access keys</li>
<li><strong>Start Uploading</strong>: Use the S3 API or web interface</li>
</ol>
<h3 id="integration-examples">Integration Examples</h3>
<h4 id="python-sdk">Python SDK</h4>
<div class="highlight"><pre tabindex="0" style="color:#f8f8f2;background-color:#272822;-moz-tab-size:4;-o-tab-size:4;tab-size:4;"><code class="language-python" data-lang="python"><span style="display:flex;"><span><span style="color:#f92672">import</span> boto3
</span></span><span style="display:flex;"><span>
</span></span><span style="display:flex;"><span><span style="color:#75715e"># Configure the client</span>
</span></span><span style="display:flex;"><span>s3_client <span style="color:#f92672">=</span> boto3<span style="color:#f92672">.</span>client(
</span></span><span style="display:flex;"><span>    <span style="color:#e6db74">&#39;s3&#39;</span>,
</span></span><span style="display:flex;"><span>    endpoint_url<span style="color:#f92672">=</span><span style="color:#e6db74">&#39;https://objectspaces.whitesky.cloud&#39;</span>,
</span></span><span style="display:flex;"><span>    aws_access_key_id<span style="color:#f92672">=</span><span style="color:#e6db74">&#39;your-access-key&#39;</span>,
</span></span><span style="display:flex;"><span>    aws_secret_access_key<span style="color:#f92672">=</span><span style="color:#e6db74">&#39;your-secret-key&#39;</span>
</span></span><span style="display:flex;"><span>)
</span></span><span style="display:flex;"><span>
</span></span><span style="display:flex;"><span><span style="color:#75715e"># Upload a file</span>
</span></span><span style="display:flex;"><span>s3_client<span style="color:#f92672">.</span>upload_file(<span style="color:#e6db74">&#39;local-file.txt&#39;</span>, <span style="color:#e6db74">&#39;my-bucket&#39;</span>, <span style="color:#e6db74">&#39;remote-file.txt&#39;</span>)
</span></span></code></pre></div><h4 id="cli-usage">CLI Usage</h4>
<div class="highlight"><pre tabindex="0" style="color:#f8f8f2;background-color:#272822;-moz-tab-size:4;-o-tab-size:4;tab-size:4;"><code class="language-bash" data-lang="bash"><span style="display:flex;"><span><span style="color:#75715e"># Configure AWS CLI</span>
</span></span><span style="display:flex;"><span>aws configure set aws_access_key_id your-access-key
</span></span><span style="display:flex;"><span>aws configure set aws_secret_access_key your-secret-key
</span></span><span style="display:flex;"><span>aws configure set default.s3.endpoint_url https://objectspaces.whitesky.cloud
</span></span><span style="display:flex;"><span>
</span></span><span style="display:flex;"><span><span style="color:#75715e"># Create a bucket</span>
</span></span><span style="display:flex;"><span>aws s3 mb s3://my-bucket
</span></span><span style="display:flex;"><span>
</span></span><span style="display:flex;"><span><span style="color:#75715e"># Upload files</span>
</span></span><span style="display:flex;"><span>aws s3 sync ./local-folder s3://my-bucket/
</span></span></code></pre></div><h2 id="monitoring-and-management">Monitoring and Management</h2>
<h3 id="metrics-and-monitoring">Metrics and Monitoring</h3>
<p>Track your ObjectSpace usage:</p>
<ul>
<li><strong>Storage utilization</strong> and growth trends</li>
<li><strong>Request metrics</strong> and performance</li>
<li><strong>Error rates</strong> and availability</li>
<li><strong>Cost analysis</strong> and optimization opportunities</li>
</ul>
<h3 id="management-tools">Management Tools</h3>
<p>Manage your ObjectSpaces efficiently:</p>
<ul>
<li><strong>Web-based portal</strong> for visual management</li>
<li><strong>CLI tools</strong> for automation</li>
<li><strong>API access</strong> for custom integrations</li>
<li><strong>Terraform provider</strong> for infrastructure as code</li>
</ul>
<h2 id="conclusion">Conclusion</h2>
<p>ObjectSpaces provide the scalable, reliable, and feature-rich object storage foundation that modern applications require. With S3 compatibility, enterprise features like versioning and object locking, and the ability to scale to 360PB, ObjectSpaces can handle any storage challenge.</p>
<p>Ready to get started with ObjectSpaces? <a href="/contact">Contact our team</a> to learn more about how ObjectSpaces can simplify your storage strategy while reducing costs and improving reliability.</p>

        </div>
        
        
        <div class="article-footer" style="margin-top: 3rem; padding-top: 2rem; border-top: 1px solid #dbdbdb;">
          <div class="level">
            <div class="level-left">
              
              <div class="level-item">
                <div class="media">
                  <div class="media-content">
                    <p class="title is-6">Storage Team</p>
                    <p class="subtitle is-7 has-text-grey">Author</p>
                  </div>
                </div>
              </div>
              
            </div>
            <div class="level-right">
              <div class="level-item">
                <div class="field is-grouped">
                  <p class="control">
                    <a class="button is-primary is-outlined" href="/blog">
                      <span class="icon">
                        <i class="fas fa-arrow-left"></i>
                      </span>
                      <span>Back to Blogs</span>
                    </a>
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    
    
    
  </div>
</section>



<footer class="footer footer-dark">
  <div class="container">
    <div class="columns">
      <div class="column">
        <div class="footer-logo">
          <img src="/images/logos/ws_logo_white_vertical_v1.webp">
        </div>
      </div>
        <div class="column">
          <div class="footer-column">
            <div class="footer-header">
                <h3>Product</h3>
            </div>
            <ul class="link-list">
              <li>
                <a href="/">
                  Discover features
                </a>
              </li>
              <li>
                <a href="/">
                  Why choose our product?
                </a>
              </li>
              <li>
                <a href="/">
                  Compare features
                </a>
              </li>
              <li>
                <a href="/">
                  Our roadmap
                </a>
              </li>
              <li>
                <a href="/agb">
                  AGB
                </a>
              </li>
            </ul>
          </div>
        </div>
        <div class="column">
          <div class="footer-column">
            <div class="footer-header">
                <h3>Docs</h3>
            </div>
            <ul class="link-list">
              <li>
                <a href="/">
                  Get started
                </a>
              </li>
              <li>
                <a href="/">
                  User guides
                </a>
              </li>
              <li>
                <a href="/">
                  Admin guide
                </a>
              </li>
              <li>
                <a href="/">
                  Developers
                </a>
              </li>
            </ul>
          </div>
        </div>
        <div class="column">
          <div class="footer-column">
            <div class="footer-header">
                <h3>Blog</h3>
            </div>
            <ul class="link-list">
              <li>
                <a href="/blog/first">
                  Latest news
                </a>
              </li>
              <li>
                <a href="/blog/second">
                  Tech articles
                </a>
              </li>
            </ul>
          </div>
        </div>
      <div class="column">
        <div class="footer-column">
          <div class="footer-header">
            <h3>Follow Us</h3>
            <nav class="level is-mobile">
              <div class="level-left">
                <a class="level-item" href="https://www.facebook.com/whitesky.fb" target="_blank">
                  <span class="icon"><i class="fa fa-facebook"></i></span>
                </a>
                <a class="level-item" href="https://twitter.com/StefMa91" target="_blank">
                  <span class="icon"><i class="fa fa-twitter"></i></span>
                </a>
                <a class="level-item" href="https://www.linkedin.com/company/whitesky-cloud" target="_blank">
                  <span class="icon"><i class="fa fa-linkedin"></i></span>
                </a>
              </div>
            </nav>
          </div>
        </div>
      </div>
    </div>
  </div>
</footer>





    
    <div id="backtotop"><a href="#"></a></div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.6.1/jquery.min.js"></script>
<script src="https://unpkg.com/feather-icons"></script>
<script src="/js/fresh.js"></script>
<script src="/js/jquery.panelslider.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/modernizr/2.8.3/modernizr.min.js"></script>

  </body>
</html>
